import os
import shutil
from pathlib import Path

# Libraries for different file types
try:
    from docx import Document
except ImportError:
    print("Install python-docx: pip install python-docx")
    Document = None

try:
    import PyPDF2
except ImportError:
    print("Install PyPDF2: pip install PyPDF2")
    PyPDF2 = None

try:
    import markdown
except ImportError:
    print("Install markdown: pip install markdown")
    markdown = None

def convert_docx_to_text(file_path):
    """Convert DOCX to text"""
    if Document is None:
        return f"Error: python-docx not installed for {file_path}"
    
    try:
        doc = Document(file_path)
        text = []
        for paragraph in doc.paragraphs:
            text.append(paragraph.text)
        return '\n'.join(text)
    except Exception as e:
        return f"Error converting {file_path}: {str(e)}"

def convert_pdf_to_text(file_path):
    """Convert PDF to text"""
    if PyPDF2 is None:
        return f"Error: PyPDF2 not installed for {file_path}"
    
    try:
        text = []
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text.append(page.extract_text())
        return '\n'.join(text)
    except Exception as e:
        return f"Error converting {file_path}: {str(e)}"

def convert_md_to_text(file_path):
    """Convert Markdown to plain text"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        # Simple conversion - just return the markdown as-is (it's already text)
        # For more advanced conversion, you could use markdown library
        return content
    except Exception as e:
        return f"Error converting {file_path}: {str(e)}"

def main():
    # Get the current directory
    current_dir = Path.cwd()
    
    # Create output directory
    output_dir = current_dir / "TEXT_CONVERTED"
    output_dir.mkdir(exist_ok=True)
    
    print(f"Processing files in: {current_dir}")
    print(f"Output directory: {output_dir}")
    print("-" * 50)
    
    # Process each file
    for file_path in current_dir.iterdir():
        if file_path.is_file() and file_path.name != 'convert_to_text.py':
            output_file = output_dir / f"{file_path.stem}_converted.txt"
            
            # Skip if already a .txt file
            if file_path.suffix.lower() == '.txt':
                print(f"Copying: {file_path.name}")
                shutil.copy2(file_path, output_dir / file_path.name)
                continue
            
            print(f"Converting: {file_path.name}")
            
            # Convert based on file type
            if file_path.suffix.lower() == '.docx':
                content = convert_docx_to_text(file_path)
            elif file_path.suffix.lower() == '.pdf':
                content = convert_pdf_to_text(file_path)
            elif file_path.suffix.lower() == '.md':
                content = convert_md_to_text(file_path)
            elif file_path.suffix.lower() == '.pub':
                content = f"Publisher file: {file_path.name}\n[Publisher files require special software to convert]"
            else:
                # Try to read as text
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except:
                    content = f"Could not convert: {file_path.name}"
            
            # Write the converted content
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✓ Saved as: {output_file.name}")
            except Exception as e:
                print(f"  ✗ Error saving: {str(e)}")
    
    print("-" * 50)
    print("Conversion complete!")
    print(f"Check the '{output_dir.name}' folder for converted files.")

if __name__ == "__main__":
    main()
