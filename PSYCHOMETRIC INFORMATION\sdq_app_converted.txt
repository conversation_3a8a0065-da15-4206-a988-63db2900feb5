<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>SDQ – Parent / Teacher / Youth</title>

  <!--
  ///////////////////////////////////////////////////////////////////////////////
  // COPYRIGHT NOTICE AND ACKNOWLEDGEMENT
  ///////////////////////////////////////////////////////////////////////////////

  The Strengths and Difficulties Questionnaire (SDQ) is copyrighted material
  owned by Youthinmind Ltd.

  © Youthinmind Ltd. All rights reserved.

  This electronic implementation is used with permission and acknowledgement
  of the copyright holder. The SDQ questionnaire content, scoring algorithms,
  and interpretation guidelines remain the intellectual property of Youthinmind Ltd.

  For more information about the SDQ, licensing, or permissions:
  - Website: https://www.sdqinfo.org/
  - Email: <EMAIL>

  ACKNOWLEDGEMENT:
  I acknowledge that the SDQ is copyrighted material owned by Youthinmind,
  and I am committed to including all required copyright and attribution
  notices in this electronic implementation.

  This implementation is for non-profit, internal, compliance-driven use
  within healthcare settings for clinical assessment purposes.

  ///////////////////////////////////////////////////////////////////////////////
  -->
  <style>
    :root{--bg:#0b1020;--card:#121936;--ink:#e9eefc;--muted:#9fb2ff;--hot:#e85d5d;--warm:#f0c24b;--cool:#58c07b;}
    *{box-sizing:border-box}body{margin:0;background:var(--bg);color:var(--ink);font:14px/1.5 system-ui,Segoe UI,Roboto,Helvetica,Arial}
    header{padding:20px;display:flex;gap:12px;align-items:center;justify-content:space-between;background:#0f1730;border-bottom:1px solid #1c2550}
    h1{margin:0;font-size:18px}
    .container{max-width:1100px;margin:24px auto;padding:0 16px}
    .card{background:var(--card);border:1px solid #1d2658;border-radius:16px;box-shadow:0 12px 30px rgba(0,0,0,.25)}
    .row{display:grid;gap:16px}
    .row.cols-2{grid-template-columns:1fr 1fr}
    .row.cols-3{grid-template-columns:1fr 1fr 1fr}
    .pad{padding:16px}
    label{display:block;margin:0 0 6px 2px;color:var(--muted);font-size:12px}
    select,button,input[type="text"]{width:100%;padding:10px 12px;border-radius:10px;border:1px solid #2a3476;background:#0f1730;color:var(--ink);outline:none}
    button{cursor:pointer;border:1px solid #33409a;background:#1a2456}
    button:hover{filter:brightness(1.1)}
    table{width:100%;border-collapse:collapse;border-spacing:0}
    th,td{padding:10px;border-bottom:1px solid #1b2350;text-align:left}
    th{color:var(--muted);font-weight:600}
    .pill{display:inline-block;padding:2px 8px;border-radius:999px;font-size:12px}
    .ok{background:rgba(88,192,123,.2);color:#9be3b5;border:1px solid #2d8b57}
    .warn{background:rgba(240,194,75,.2);color:#ffe199;border:1px solid #aa7e21}
    .bad{background:rgba(232,93,93,.25);color:#ffc1c1;border:1px solid #a83c3c}
    .low{background:rgba(99,153,230,.18);color:#bcd3ff;border:1px solid #4c6cc9}
    .grid-scores{display:grid;grid-template-columns:repeat(3,minmax(0,1fr));gap:10px}
    .score{padding:10px;border:1px solid #273081;border-radius:12px;background:#0d1530}
    .score h4{margin:0 0 4px 0}
    footer{padding:16px;color:#9bb0ff;text-align:center;font-size:12px}
    canvas{width:100%!important;height:280px!important;background:#0d1530;border:1px solid #22306a;border-radius:12px}
    .hint{color:#a6b6ff;font-size:12px}
    .flex{display:flex;gap:12px;align-items:center;flex-wrap:wrap}
    .spacer{height:8px}
    .small{font-size:12px;color:#9fb2ff}

    /* Mobile optimizations */
    @media (max-width: 768px) {
      header{flex-direction:column;gap:8px;padding:16px}
      h1{font-size:16px;text-align:center}
      .flex{justify-content:center;gap:8px}
      .container{margin:16px auto;padding:0 12px}
      .row.cols-2{grid-template-columns:1fr}
      .row.cols-3{grid-template-columns:1fr}
      table{font-size:12px}
      th,td{padding:8px 4px}
      select,button,input[type="text"]{padding:12px 8px;font-size:16px}
      .score{padding:8px}
      canvas{height:200px!important}
      .grid-scores{grid-template-columns:1fr 1fr;gap:8px}
    }

    @media (max-width: 480px) {
      header{padding:12px}
      .container{padding:0 8px}
      table{font-size:11px}
      th,td{padding:6px 2px}
      .flex{flex-direction:column;width:100%}
      .flex select, .flex button{margin-bottom:8px}
      .grid-scores{grid-template-columns:1fr}
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
<header class="card pad">
  <h1>Strengths and Difficulties Questionnaire (SDQ)</h1>
  <div class="flex">
    <label for="version">Version</label>
    <select id="version"></select>
    <label for="mode">Mode</label>
    <select id="mode">
      <option value="intake">Intake</option>
      <option value="followup">Follow-up</option>
      <option value="closure">Closure</option>
    </select>
    <button id="reset">Reset form</button>
    <button id="exportCsv">Export CSV</button>
  </div>
</header>

<div class="container">
  <div class="card pad">
    <div class="row cols-3">
      <div>
        <label>Participant Initials</label>
        <input id="participant" type="text" placeholder="e.g. J.D." maxlength="10">
      </div>
      <div>
        <label>Date</label>
        <input id="when" type="text" placeholder="YYYY-MM-DD">
      </div>
      <div>
        <label>Assessor</label>
        <input id="assessor" type="text" placeholder="Parent / Teacher / Youth">
      </div>
    </div>

    <div class="spacer"></div>
    <div class="hint">
      <strong>Current Version:</strong> <span id="versionDisplay"></span><br>
      Select a response for each item. Scoring auto-updates. Questions adapt to the selected version.
    </div>
    <div class="spacer"></div>

    <table id="table">
      <thead>
        <tr>
          <th style="width:60px">Item</th>
          <th>Question</th>
          <th style="width:200px">Response</th>
          <th style="width:90px">Score</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>

    <div class="spacer"></div>
    <div class="row cols-2">
      <div>
        <h3 class="small">Impact (not part of Total Difficulties)</h3>
        <div id="impactContainer" class="row"></div>
        <div class="spacer"></div>
        <div class="row cols-3">
          <div>
            <label>Impact Total</label>
            <div id="impactTotal" class="score"><strong>0</strong></div>
          </div>
        </div>
      </div>
      <div>
        <h3 class="small">Scores & bands</h3>
        <div class="grid-scores" id="scoreGrid"></div>
      </div>
    </div>

    <div class="spacer"></div>
    <h3 class="small">Chart</h3>
    <canvas id="chart"></canvas>
  </div>
</div>

<footer>
  <div style="margin-bottom: 12px;">
    <strong>Copyright Notice:</strong> The Strengths and Difficulties Questionnaire (SDQ) is copyrighted material owned by Youthinmind Ltd.
    This electronic implementation is used with permission and acknowledgement of the copyright holder.
  </div>
  <div style="margin-bottom: 8px;">
    © Youthinmind Ltd. All rights reserved. | For more information: <a href="https://www.sdqinfo.org/" target="_blank" style="color: #9fb2ff;">www.sdqinfo.org</a>
  </div>
  <div style="font-size: 11px; color: #7a8ccc;">
    Offline single-file app. Host on Netlify by dropping this HTML. No server required.
  </div>
</footer>

<script>
// ----- SDQ Question Texts -----
const SDQ_QUESTIONS = {
  parent: [
    "Considerate of other people's feelings",
    "Restless, overactive, cannot stay still for long",
    "Often complains of headaches, stomach-aches or sickness",
    "Shares readily with other children, for example toys, treats, pencils",
    "Often loses temper",
    "Would rather be alone than with other children",
    "Generally well behaved, usually does what adults request",
    "Many worries or often seems worried",
    "Helpful if someone is hurt, upset or feeling ill",
    "Constantly fidgeting or squirming",
    "Has at least one good friend",
    "Often fights with other children or bullies them",
    "Often unhappy, depressed or tearful",
    "Generally liked by other children",
    "Easily distracted, concentration wanders",
    "Nervous in new situations, easily loses confidence",
    "Kind to younger children",
    "Often argumentative with adults",
    "Picked on or bullied by other children",
    "Often offers to help others (parents, teachers, other children)",
    "Thinks things out before acting",
    "Steals from home, school or elsewhere",
    "Gets along better with adults than with other children",
    "Many fears, easily scared",
    "Good attention span, sees chores or homework through to the end"
  ],
  teacher: [
    "Considerate of other people's feelings",
    "Restless, overactive, cannot stay still for long",
    "Often complains of headaches, stomach-aches or sickness",
    "Shares readily with other children, for example toys, treats, pencils",
    "Often loses temper",
    "Would rather be alone than with other children",
    "Generally well behaved, usually does what adults request",
    "Many worries or often seems worried",
    "Helpful if someone is hurt, upset or feeling ill",
    "Constantly fidgeting or squirming",
    "Has at least one good friend",
    "Often fights with other children or bullies them",
    "Often unhappy, depressed or tearful",
    "Generally liked by other children",
    "Easily distracted, concentration wanders",
    "Nervous in new situations, easily loses confidence",
    "Kind to younger children",
    "Often argumentative with adults",
    "Picked on or bullied by other children",
    "Often offers to help others (parents, teachers, other children)",
    "Thinks things out before acting",
    "Steals from home, school or elsewhere",
    "Gets along better with adults than with other children",
    "Many fears, easily scared",
    "Good attention span, sees chores or homework through to the end"
  ],
  youth: [
    "I try to be nice to other people. I care about their feelings",
    "I am restless, I cannot stay still for long",
    "I get a lot of headaches, stomach-aches or sickness",
    "I usually share with others, for example CD's, games, food",
    "I get very angry and often lose my temper",
    "I would rather be alone than with people of my age",
    "I usually do as I am told",
    "I worry a lot",
    "I am helpful if someone is hurt, upset or feeling ill",
    "I am constantly fidgeting or squirming",
    "I have one good friend or more",
    "I fight a lot. I can make other people do what I want",
    "I am often unhappy, depressed or tearful",
    "Other people my age generally like me",
    "I am easily distracted, I find it difficult to concentrate",
    "I am nervous in new situations. I easily lose confidence",
    "I am kind to younger children",
    "I am often accused of lying or cheating",
    "Other children or young people pick on me or bully me",
    "I often offer to help others (parents, teachers, children)",
    "I think before I do things",
    "I take things that are not mine from home, school or elsewhere",
    "I get along better with adults than with people my own age",
    "I have many fears, I am easily scared",
    "I finish the work I'm doing. My attention is good"
  ]
};

// ----- Version Profiles -----
const VERSION_PROFILES = {
  Parent_4_17:   { label:"Parent (4–17)",   items:25, reverse:[7,11,14,21,25], impactRows:4, thresholds:"generic4_17", questions:"parent", prefix:"Does your child" },
  Teacher_4_17:  { label:"Teacher (4–17)",  items:25, reverse:[7,11,14,21,25], impactRows:2, thresholds:"generic4_17", questions:"teacher", prefix:"Does the child" },
  Youth_11_17:   { label:"Youth (11–17)",   items:25, reverse:[7,11,14,21,25], impactRows:4, thresholds:"generic4_17", questions:"youth", prefix:"" },
  Parent_2_4:    { label:"Parent (2–4)",    items:22, reverse:[], impactRows:3, thresholds:"nursery2_4", questions:"parent", prefix:"Does your child" }
};

// Threshold sets for different age groups
const THRESHOLD_SETS = {
  generic4_17: {
    Emotional: {normalMax:5, borderlineMax:6, dir:1},
    Conduct:   {normalMax:3, borderlineMax:4, dir:1},
    Hyperactivity:{normalMax:5, borderlineMax:6, dir:1},
    Peer:      {normalMax:2, borderlineMax:3, dir:1},
    Prosocial: {normalMax:10,borderlineMax:10,dir:-1},
    Total:     {normalMax:13,borderlineMax:16,dir:1},
  },
  nursery2_4: {
    Emotional: {normalMax:4, borderlineMax:5, dir:1},
    Conduct:   {normalMax:3, borderlineMax:4, dir:1},
    Hyperactivity:{normalMax:4, borderlineMax:5, dir:1},
    Peer:      {normalMax:3, borderlineMax:4, dir:1},
    Prosocial: {normalMax:7, borderlineMax:7, dir:-1},
    Total:     {normalMax:12,borderlineMax:15,dir:1},
  }
};

// ----- Dynamic Config -----
function currentProfile(){ return VERSION_PROFILES[state.version] || VERSION_PROFILES.Parent_4_17; }
function currentThresholds(){
  const p = currentProfile();
  return THRESHOLD_SETS[p.thresholds] || THRESHOLD_SETS.generic4_17;
}
function currentItems(){ return currentProfile().items; }
function currentReverseSet(){ return new Set(currentProfile().reverse); }
function currentImpactRows(){ return currentProfile().impactRows; }
function currentQuestions(){
  const p = currentProfile();
  return SDQ_QUESTIONS[p.questions] || SDQ_QUESTIONS.parent;
}
function getQuestionText(itemNo){
  const questions = currentQuestions();
  const profile = currentProfile();
  const questionText = questions[itemNo - 1] || `Item ${itemNo}`;

  if(profile.prefix && profile.questions !== "youth") {
    return `${profile.prefix} ${questionText.toLowerCase()}?`;
  }
  return profile.questions === "youth" ? questionText + "?" : questionText + "?";
}

// ----- Static Config -----
const RESP = ["","Not True","Somewhat True","Certainly True"];
const IMP_RESP = ["","Not at all","Only a little","Quite a lot","A great deal"];

const SUBSCALES = {
  Emotional: [3,8,13,16,24],
  Conduct: [5,7,12,18,22],
  Hyperactivity: [2,10,15,21,25],
  Peer: [6,11,14,19,23],
  Prosocial: [1,4,9,17,20],
};

// ----- State -----
// Read query params (?v=Teacher_4_17&mode=followup)
const qp = new URLSearchParams(location.search);
const state = {
  version: qp.get("v") && VERSION_PROFILES[qp.get("v")] ? qp.get("v") : "Parent_4_17",
  mode: qp.get("mode") || "intake",
  responses: [],
  impact: [],
};

// ----- Utils -----
function scoreItem(itemNo, resp){
  if(!resp) return 0;
  const base = (resp==="Not True"?0: resp==="Somewhat True"?1:2);
  return currentReverseSet().has(itemNo) ? 2-base : base;
}

function bandFor(scale, value){
  const t = currentThresholds()[scale];
  if(!t) return ["",""];
  if(t.dir===1){
    if(value<=t.normalMax) return ["Normal","ok"];
    if(value<=t.borderlineMax) return ["Borderline","warn"];
    return ["High","bad"];
  }else{
    // Prosocial, high = good
    if(value>=6) return ["Normal","ok"];
    if(value===5) return ["Borderline","warn"];
    return ["Low","low"];
  }
}

function byId(id){return document.getElementById(id)}

function calcScores(){
  // item scores
  const N = currentItems();
  const scores = state.responses.slice(0, N).map((r,i)=>scoreItem(i+1,r));
  // subs
  const sums = {};
  for(const [name, items] of Object.entries(SUBSCALES)){
    // Only include items that exist in current version
    sums[name] = items.filter(n => n <= N).reduce((a,n)=>a+scores[n-1],0);
  }
  const total = sums.Emotional + sums.Conduct + sums.Hyperactivity + sums.Peer;
  const external = sums.Conduct + sums.Hyperactivity;
  const internal = sums.Emotional + sums.Peer;
  const impactTotal = state.impact.reduce((a,v)=> a + (v==="Not at all"?0:v==="Only a little"?1:v==="Quite a lot"?2:v==="A great deal"?3:0),0);
  return {scores, sums, total, external, internal, impactTotal};
}

function renderTable(){
  const tb = byId("table").querySelector("tbody");
  tb.innerHTML = "";
  const N = currentItems();
  // Ensure responses array is correct length
  while(state.responses.length < N) state.responses.push("");
  state.responses = state.responses.slice(0, N);

  for(let i=1;i<=N;i++){
    const tr = document.createElement("tr");

    // Item number
    const tdNo = document.createElement("td");
    tdNo.textContent = i;

    // Question text
    const tdQuestion = document.createElement("td");
    tdQuestion.textContent = getQuestionText(i);
    tdQuestion.style.fontSize = "13px";

    // Response dropdown
    const tdResp = document.createElement("td");
    const sel = document.createElement("select");
    RESP.forEach(v=>{
      const opt = document.createElement("option");
      opt.value = v; opt.textContent = v || "—";
      sel.appendChild(opt);
    });
    sel.value = state.responses[i-1] || "";
    sel.onchange = (e)=>{state.responses[i-1]=e.target.value; update();};
    tdResp.appendChild(sel);

    // Score
    const tdScore = document.createElement("td");
    tdScore.dataset.scoreCell = i;
    tdScore.textContent = "0";

    tr.appendChild(tdNo);
    tr.appendChild(tdQuestion);
    tr.appendChild(tdResp);
    tr.appendChild(tdScore);
    tb.appendChild(tr);
  }
}

function renderImpact(){
  const div = byId("impactContainer");
  div.innerHTML = "";
  const rows = currentImpactRows();
  state.impact = Array(rows).fill("");
  for(let i=0;i<rows;i++){
    const wrap = document.createElement("div");
    wrap.className="row cols-3";
    const lab = document.createElement("label");
    lab.textContent = `Impact ${i+1}`;
    const sel = document.createElement("select");
    IMP_RESP.forEach(v=>{
      const opt = document.createElement("option");
      opt.value = v; opt.textContent = v || "—";
      sel.appendChild(opt);
    });
    sel.onchange = (e)=>{state.impact[i]=e.target.value; update();};
    wrap.appendChild(lab);
    wrap.appendChild(sel);
    div.appendChild(wrap);
  }

  // Add follow-up specific question
  if (state.mode === "followup") {
    const wrap = document.createElement("div");
    wrap.className = "row cols-3";
    wrap.innerHTML = `
      <label>Change since last assessment</label>
      <select id="changeFlag">
        <option value="">—</option>
        <option value="much_better">Much better</option>
        <option value="a_bit_better">A bit better</option>
        <option value="about_the_same">About the same</option>
        <option value="a_bit_worse">A bit worse</option>
        <option value="much_worse">Much worse</option>
      </select>
      <div></div>
    `;
    div.appendChild(wrap);
  }
}

function scoreGridCell(label, value){
  const [band, cls] = bandFor(label==="Externalising"||label==="Internalising" ? "Total" : label, value) || ["",""];
  return `<div class="score"><h4>${label}</h4><div><strong>${value}</strong> <span class="pill ${cls}">${band||""}</span></div></div>`;
}

let chart;
function renderScores(){
  const out = calcScores();
  const N = currentItems();
  // update per item
  for(let i=1;i<=N;i++){
    const td = document.querySelector(`td[data-score-cell="${i}"]`);
    if(td) td.textContent = scoreItem(i, state.responses[i-1]);
  }
  // impact
  byId("impactTotal").innerHTML = `<strong>${out.impactTotal}</strong>`;

  // score grid
  const g = byId("scoreGrid");
  g.innerHTML = [
    scoreGridCell("Emotional", out.sums.Emotional),
    scoreGridCell("Conduct", out.sums.Conduct),
    scoreGridCell("Hyperactivity", out.sums.Hyperactivity),
    scoreGridCell("Peer", out.sums.Peer),
    scoreGridCell("Prosocial", out.sums.Prosocial),
    scoreGridCell("Total", out.total),
    scoreGridCell("Externalising", out.external),
    scoreGridCell("Internalising", out.internal),
  ].join("");

  // chart
  const data = {
    labels:["Emotional","Conduct","Hyperactivity","Peer","Prosocial","Total"],
    values:[out.sums.Emotional,out.sums.Conduct,out.sums.Hyperactivity,out.sums.Peer,out.sums.Prosocial,out.total]
  };
  if(!chart){
    chart = new Chart(byId("chart").getContext("2d"), {
      type:"bar",
      data: {
        labels: data.labels,
        datasets: [{label:"Scores", data: data.values}]
      },
      options:{
        responsive:true,
        plugins:{legend:{display:false}, tooltip:{enabled:true}},
        scales:{x:{grid:{display:false}}, y:{beginAtZero:true}}
      }
    });
  }else{
    chart.data.datasets[0].data = data.values;
    chart.update();
  }
}

function updateVersionDisplay(){
  const profile = currentProfile();
  const prefix = profile.prefix ? `${profile.prefix} questions` : "Self-report questions";
  byId("versionDisplay").textContent = `${profile.label} - ${prefix}`;
}

function update(){
  updateVersionDisplay();
  renderScores();
}

// Reset/export
byId("reset").onclick = ()=>{
  const N = currentItems();
  state.responses = Array(N).fill("");
  renderTable();
  renderImpact();
  renderScores();
};

byId("exportCsv").onclick = ()=>{
  const {sums,total,external,internal,impactTotal} = calcScores();
  const fileSafe = s => (s||"").trim().replace(/\s+/g,"_");
  const changeFlag = byId("changeFlag") ? byId("changeFlag").value : "";

  const csv = [
    ["Participant Initials", byId("participant").value],
    ["Date", byId("when").value],
    ["Assessor", byId("assessor").value],
    ["Version", currentProfile().label],
    ["Mode", state.mode],
    ...(changeFlag ? [["Change Since Last", changeFlag]] : []),
    [],
    ["Scale", "Score", "Band"],
    ["Emotional", sums.Emotional, bandFor("Emotional", sums.Emotional)[0]],
    ["Conduct", sums.Conduct, bandFor("Conduct", sums.Conduct)[0]],
    ["Hyperactivity", sums.Hyperactivity, bandFor("Hyperactivity", sums.Hyperactivity)[0]],
    ["Peer", sums.Peer, bandFor("Peer", sums.Peer)[0]],
    ["Prosocial", sums.Prosocial, bandFor("Prosocial", sums.Prosocial)[0]],
    ["Total Difficulties", total, bandFor("Total", total)[0]],
    ["Externalising", external, bandFor("Total", external)[0]],
    ["Internalising", internal, bandFor("Total", internal)[0]],
    ["Impact Total", impactTotal, ""]
  ].map(r=>r.join(",")).join("\n");

  const blob = new Blob([csv], {type:"text/csv"});
  const a = document.createElement("a");
  a.href = URL.createObjectURL(blob);
  a.download = `SDQ_${state.version}_${state.mode}_${fileSafe(byId("participant").value)}.csv`;
  a.click();
};

// Populate version select from profiles
const vSel = byId("version");
vSel.innerHTML = Object.entries(VERSION_PROFILES)
  .map(([k,p])=>`<option value="${k}">${p.label}</option>`).join("");
vSel.value = state.version;

// Mode select initialization
byId("mode").value = state.mode;

// Version switch
byId("version").onchange = (e)=>{
  state.version = e.target.value;
  renderTable();
  renderImpact();
  update();
};

// Mode switch
byId("mode").onchange = (e)=>{
  state.mode = e.target.value;
  renderImpact();
  update();
};

// Init
renderTable();
renderImpact();
update();
</script>
</body>
</html>
