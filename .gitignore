# Netlify
.netlify/
dist/
.env
.env.local
.env.production

# Node.js (if using build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.backup

# HoNOSCA Data exports (for privacy)
*.csv
exports/
data/

# Copyright notice: HoNOSCA © Royal College of Psychiatrists
# This .gitignore helps protect sensitive data while maintaining copyright compliance
