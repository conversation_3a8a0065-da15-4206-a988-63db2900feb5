# PowerShell script to run Ollama with memory limits

# Define default parameters
param(
    [int]$MaxLoadedModels = 1,
    [int]$NumParallel = 1,
    [string]$MaxMemory = "4gb"
)

# Function to check if <PERSON>llama is already running
function Test-OllamaRunning {
    $ollamaProcess = Get-Process -Name "ollama" -ErrorAction SilentlyContinue
    return $null -ne $ollamaProcess
}

# Function to stop Ollama if it's running
function Stop-Ollama {
    Write-Host "Stopping Ollama service..."
    ollama stop
    
    # Wait for <PERSON>lla<PERSON> to stop
    $maxWaitTime = 30  # seconds
    $waited = 0
    while (Test-OllamaRunning) {
        Start-Sleep -Seconds 1
        $waited++
        if ($waited -ge $maxWaitTime) {
            Write-Host "Warning: Ollama did not stop within $maxWaitTime seconds."
            Write-Host "Attempting to force stop Ollama processes..."
            Stop-Process -Name "ollama" -Force -ErrorAction SilentlyContinue
            break
        }
    }
}

# Check if <PERSON>lla<PERSON> is already running
if (Test-OllamaRunning) {
    Write-Host "Ollama is currently running."
    $stopOllama = Read-Host "Do you want to stop it and restart with memory limits? (y/n)"
    if ($stopOllama -eq "y") {
        Stop-Ollama
    } else {
        Write-Host "Exiting without making changes."
        exit 0
    }
}

# Build the command to run Ollama with memory limits
$ollamaCommand = "ollama serve --max-loaded-models $MaxLoadedModels --num-parallel $NumParallel --max-memory $MaxMemory"

# Display the command that will be run
Write-Host "Running Ollama with the following limits:"
Write-Host "  Max Loaded Models: $MaxLoadedModels"
Write-Host "  Max Parallel Requests: $NumParallel"
Write-Host "  Max Memory: $MaxMemory"
Write-Host ""
Write-Host "Command: $ollamaCommand"
Write-Host ""

# Run Ollama with memory limits
Write-Host "Starting Ollama with memory limits..."
Invoke-Expression $ollamaCommand

# Note: This script will keep running until Ollama is stopped or the script is interrupted
Write-Host "Ollama is running with memory limits. Press Ctrl+C to stop."