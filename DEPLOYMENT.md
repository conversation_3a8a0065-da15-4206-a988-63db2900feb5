# Netlify Deployment Guide for HoNOSCA Application

## Copyright Compliance Notice
**Before deploying:** Ensure you have appropriate permissions from the Royal College of Psychiatrists for your intended use case. HoNOSCA is copyrighted material and this implementation includes all required copyright notices.

## Quick Deployment Options

### Option 1: Drag & Drop (Simplest)
1. Go to [Netlify](https://netlify.com)
2. Sign up or log in
3. Drag the `honosca_app.html` file directly to the Netlify dashboard
4. Your app will be live instantly with a random URL
5. Optionally customize the domain name

### Option 2: Git Repository Deployment (Recommended)
1. Push this repository to GitHub, GitLab, or Bitbucket
2. Connect your Netlify account to your Git provider
3. Import this repository in Netlify
4. Netlify will automatically deploy using the `netlify.toml` configuration
5. Automatic deployments on every push

### Option 3: Netlify CLI
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy from this directory
netlify deploy

# Deploy to production
netlify deploy --prod
```

## Configuration Details

### Netlify Settings
The `netlify.toml` file includes:
- ✅ Security headers (CSP, XSS protection, etc.)
- ✅ Redirect rules (/ → /honosca_app.html)
- ✅ 404 handling
- ✅ Cache control settings

### Environment Variables
No environment variables are required for this static application.

### Build Settings
- **Build Command:** None (static HTML)
- **Publish Directory:** `.` (root directory)
- **Functions Directory:** Not used

## Post-Deployment Checklist

### 1. Test Functionality
- [ ] Application loads correctly
- [ ] All 13 scales display properly
- [ ] Rating dropdowns work correctly
- [ ] Scoring calculations are accurate
- [ ] CSV export functions properly
- [ ] Charts render correctly
- [ ] Mobile responsiveness works

### 2. Verify Copyright Notices
- [ ] Copyright notice visible in HTML comments
- [ ] Footer displays Royal College of Psychiatrists attribution
- [ ] Links to rcpsych.ac.uk are working
- [ ] All required acknowledgements are present

### 3. Security Verification
- [ ] HTTPS is enabled (automatic with Netlify)
- [ ] Security headers are applied
- [ ] No external data transmission
- [ ] CSP headers prevent XSS attacks

### 4. Performance Check
- [ ] Page loads quickly (single file)
- [ ] Mobile responsive design works
- [ ] Charts and interactions are smooth
- [ ] Offline functionality works

## Custom Domain Setup

### Using Netlify Domain
1. Go to Site Settings → Domain Management
2. Click "Options" → "Edit site name"
3. Choose a meaningful name like `your-org-honosca`

### Using Custom Domain
1. Go to Site Settings → Domain Management
2. Click "Add custom domain"
3. Follow DNS configuration instructions
4. SSL certificate will be automatically provisioned

## Compliance and Legal Considerations

### Required Permissions
Before public deployment, ensure you have:
- [ ] Permission from Royal College of Psychiatrists for your use case
- [ ] Appropriate clinical/professional authorization
- [ ] Compliance with local healthcare regulations
- [ ] Data privacy compliance (GDPR, HIPAA, etc.)

### Recommended Restrictions
Consider implementing:
- Password protection for internal use
- IP restrictions for organizational access
- User authentication for clinical staff
- Audit logging for compliance

### Contact Information
For licensing and permissions:
- **Royal College of Psychiatrists:** https://www.rcpsych.ac.uk/
- **Contact:** Through official channels

## Troubleshooting

### Common Issues
1. **App not loading:** Check that `honosca_app.html` is in the root directory
2. **Charts not working:** Verify Chart.js CDN is accessible
3. **CSV export failing:** Check browser permissions for downloads
4. **Mobile issues:** Test responsive design on various devices
5. **Rating not saving:** Ensure JavaScript is enabled

### Support Resources
- Netlify Documentation: https://docs.netlify.com/
- Netlify Community: https://community.netlify.com/
- Royal College of Psychiatrists: https://www.rcpsych.ac.uk/

## Monitoring and Maintenance

### Analytics
Consider adding:
- Netlify Analytics for usage statistics
- Error monitoring for technical issues
- Performance monitoring

### Updates
- Monitor for HoNOSCA updates from Royal College of Psychiatrists
- Keep Chart.js library updated
- Review security headers periodically
- Update copyright notices as needed

## Clinical Integration

### Workflow Integration
- Use URL parameters for direct access: `?mode=followup`
- Bookmark specific assessment modes
- Export CSV data to clinical systems
- Integrate with existing assessment workflows

### Multi-Assessment Setup
- Can be used alongside other assessment tools
- Export data in compatible formats
- Maintain separate assessment records
- Follow clinical documentation standards

---

**Remember:** This deployment guide assumes you have appropriate permissions to use HoNOSCA electronically. Always comply with copyright requirements and professional guidelines.
