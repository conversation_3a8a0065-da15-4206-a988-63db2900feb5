"""
Ollama API Connection Management Module

This module provides functions to help manage API connections to Ollama.
It implements proper connection closing to help mitigate memory leaks.
"""

import requests
import json

class OllamaAPIManager:
    """
    A class to manage API connections to Ollama.
    
    This class provides methods to interact with the Ollama API while
    ensuring connections are properly closed after each request.
    """
    
    def __init__(self, base_url="http://localhost:11434"):
        """
        Initialize the Ollama API Manager.
        
        Args:
            base_url: The base URL for the Ollama API (default: http://localhost:11434)
        """
        self.base_url = base_url
        
    def _call_api(self, endpoint, data, stream=False):
        """
        Make a request to the Ollama API and ensure the connection is closed.
        
        Args:
            endpoint: The API endpoint to call
            data: The data to send in the request
            stream: Whether to stream the response
            
        Returns:
            The response from the API
        """
        url = f"{self.base_url}/{endpoint}"
        
        try:
            # Make the request
            response = requests.post(url, json=data, stream=stream)
            
            # Get the response data
            if stream:
                result = []
                for line in response.iter_lines():
                    if line:
                        result.append(json.loads(line))
                response_data = result
            else:
                response_data = response.json()
                
            return response_data
        finally:
            # Explicitly close the connection
            response.close()
            
    def generate(self, model, prompt, system=None, options=None):
        """
        Generate a response from a model.
        
        Args:
            model: The name of the model to use
            prompt: The prompt to send to the model
            system: Optional system prompt
            options: Optional generation options
            
        Returns:
            The generated response
        """
        data = {
            "model": model,
            "prompt": prompt
        }
        
        if system:
            data["system"] = system
            
        if options:
            data["options"] = options
            
        return self._call_api("api/generate", data)
    
    def chat(self, model, messages, stream=False, options=None):
        """
        Chat with a model.
        
        Args:
            model: The name of the model to use
            messages: The chat messages
            stream: Whether to stream the response
            options: Optional generation options
            
        Returns:
            The chat response
        """
        data = {
            "model": model,
            "messages": messages
        }
        
        if options:
            data["options"] = options
            
        return self._call_api("api/chat", data, stream=stream)
    
    def embeddings(self, model, prompt):
        """
        Get embeddings for a prompt.
        
        Args:
            model: The name of the model to use
            prompt: The prompt to get embeddings for
            
        Returns:
            The embeddings
        """
        data = {
            "model": model,
            "prompt": prompt
        }
        
        return self._call_api("api/embeddings", data)

# Example usage
"""
# Create an instance of the Ollama API Manager
ollama = OllamaAPIManager()

# Generate a response
response = ollama.generate(
    model="llama2",
    prompt="What is the capital of France?",
    system="You are a helpful assistant."
)

# Chat with a model
messages = [
    {"role": "user", "content": "What is the capital of France?"}
]
response = ollama.chat(model="llama2", messages=messages)

# Get embeddings
embeddings = ollama.embeddings(model="llama2", prompt="What is the capital of France?")
"""