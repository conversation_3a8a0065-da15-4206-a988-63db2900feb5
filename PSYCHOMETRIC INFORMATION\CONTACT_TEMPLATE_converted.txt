# Contact Template for Youthinmind Permission Request

## Email Template for SDQ Electronic Implementation Permission

**To:** <EMAIL>  
**Subject:** Request for Permission - Electronic Implementation of SDQ for Clinical Use

---

Dear Youthinmind Team,

I am writing to request permission for the electronic implementation of the Strengths and Difficulties Questionnaire (SDQ) for clinical assessment purposes within our healthcare organization.

### Background Information
**Name:** [Your Full Name]  
**Professional Title:** [Your Title & Registration Number]  
**Organization:** [Hospital/Clinic/Organization Name]  
**Location:** [City, State/Province, Country]  
**Email:** [Your Professional Email]  
**Phone:** [Your Contact Number]  

### Proposed Use Case
I am seeking permission to implement an electronic version of the SDQ for:
- ✅ Non-profit, internal clinical use
- ✅ Patient assessment and screening
- ✅ Treatment planning and monitoring
- ✅ Compliance with clinical documentation requirements
- ✅ Quality improvement initiatives

### Implementation Details
The electronic implementation would:
- Maintain all original SDQ content and scoring
- Include comprehensive copyright notices and attributions
- Be used exclusively within our healthcare setting
- Not be distributed or commercialized
- Comply with all licensing terms and conditions

### Previous Permission Reference
[If applicable: "I have previously received permission from <PERSON><PERSON> for [specific use case] and am now seeking to extend this to electronic implementation."]

### Commitment to Compliance
I acknowledge that:
1. The SDQ is copyrighted material owned by Youthinmind Ltd
2. All copyright and attribution notices will be maintained
3. The implementation is for legitimate clinical purposes only
4. I will comply with all licensing terms and conditions
5. I will not distribute or modify the core SDQ content

### Technical Specifications
The electronic version will:
- Be hosted securely within our organization
- Include proper data privacy protections
- Maintain the integrity of the original questionnaire
- Provide accurate scoring and interpretation
- Include links to official SDQ resources

### Request for Guidance
I would greatly appreciate your guidance on:
- Appropriate licensing terms for this use case
- Required copyright and attribution notices
- Any specific conditions or restrictions
- Ongoing compliance requirements

Thank you for your time and consideration of this request. I am committed to ensuring full compliance with all copyright requirements and professional standards.

I look forward to your response and guidance on the appropriate process for obtaining permission.

Yours sincerely,

[Your Full Name]  
[Your Professional Title & Registration]  
[Hospital/Organization Name]  
[Location]  
[Email]  
[Phone]

---

## Alternative Shorter Initial Contact

**Subject:** Inquiry - SDQ Electronic Implementation Permission

Dear Youthinmind Team,

I am a [Your Professional Title] at [Organization] seeking guidance on obtaining permission for electronic implementation of the SDQ for clinical assessment purposes within our healthcare setting.

Could you please advise on the appropriate process for requesting licensing for non-profit, internal clinical use?

I am committed to full copyright compliance and including all required attributions.

Thank you for your guidance.

Best regards,  
[Your Name]  
[Contact Information]

---

## Contact Information

**Youthinmind Ltd**
- **General Support:** <EMAIL>
- **SDQ Pro Licensing:** <EMAIL>  
- **Scoring Tools:** <EMAIL>
- **Website:** https://www.sdqinfo.org/
- **Phone:** +44 (0)20 7580 7145

## Tips for Successful Contact

1. **Be Professional:** Use official letterhead if available
2. **Be Specific:** Clearly describe your intended use case
3. **Show Respect:** Acknowledge copyright and express willingness to comply
4. **Provide Context:** Explain why electronic implementation is needed
5. **Be Patient:** Allow time for response and follow-up appropriately
6. **Keep Records:** Save all correspondence for compliance documentation

## Follow-up Actions

After receiving permission:
- [ ] Document the permission terms
- [ ] Implement required copyright notices
- [ ] Comply with any specific conditions
- [ ] Maintain ongoing communication if needed
- [ ] Review permissions periodically for updates
