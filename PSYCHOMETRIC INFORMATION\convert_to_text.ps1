# PowerShell script to convert files to text
# This script will attempt to extract text from various file formats

$currentDir = Get-Location
$outputDir = Join-Path $currentDir "TEXT_CONVERTED"

# Create output directory if it doesn't exist
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir | Out-Null
}

Write-Host "Processing files in: $currentDir" -ForegroundColor Green
Write-Host "Output directory: $outputDir" -ForegroundColor Green
Write-Host ("-" * 50)

# Get all files in current directory
$files = Get-ChildItem -Path $currentDir -File | Where-Object { $_.Name -ne "convert_to_text.ps1" }

foreach ($file in $files) {
    $outputFile = Join-Path $outputDir "$($file.BaseName)_converted.txt"
    
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        switch ($file.Extension.ToLower()) {
            ".txt" {
                # Copy text files directly
                Copy-Item $file.FullName -Destination (Join-Path $outputDir $file.Name)
                Write-Host "  ✓ Copied directly" -ForegroundColor Green
            }
            ".docx" {
                # Extract text from Word documents
                $word = New-Object -ComObject Word.Application
                $word.Visible = $false
                $doc = $word.Documents.Open($file.FullName)
                $text = $doc.Content.Text
                $doc.Close()
                $word.Quit()
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
                
                $text | Out-File -FilePath $outputFile -Encoding UTF8
                Write-Host "  ✓ Converted from DOCX" -ForegroundColor Green
            }
            ".pdf" {
                # Note: PowerShell doesn't have native PDF support
                # This will just note that it's a PDF file
                "PDF File: $($file.Name)`n`n[PDF conversion requires additional tools or libraries]" | Out-File -FilePath $outputFile -Encoding UTF8
                Write-Host "  ! PDF noted (requires manual conversion)" -ForegroundColor Yellow
            }
            ".md" {
                # Markdown files are already text, just copy
                Get-Content $file.FullName | Out-File -FilePath $outputFile -Encoding UTF8
                Write-Host "  ✓ Converted from Markdown" -ForegroundColor Green
            }
            ".pub" {
                # Publisher files need special handling
                "Publisher File: $($file.Name)`n`n[Publisher files require Microsoft Publisher for conversion]" | Out-File -FilePath $outputFile -Encoding UTF8
                Write-Host "  ! Publisher noted (requires manual conversion)" -ForegroundColor Yellow
            }
            default {
                # Try to read as text
                try {
                    Get-Content $file.FullName | Out-File -FilePath $outputFile -Encoding UTF8
                    Write-Host "  ✓ Read as text" -ForegroundColor Green
                }
                catch {
                    "Could not convert: $($file.Name)" | Out-File -FilePath $outputFile -Encoding UTF8
                    Write-Host "  ✗ Could not convert" -ForegroundColor Red
                }
            }
        }
    }
    catch {
        Write-Host "  ✗ Error: $_" -ForegroundColor Red
    }
}

Write-Host ("-" * 50)
Write-Host "Conversion complete!" -ForegroundColor Green
Write-Host "Check the 'TEXT_CONVERTED' folder for converted files." -ForegroundColor Green
