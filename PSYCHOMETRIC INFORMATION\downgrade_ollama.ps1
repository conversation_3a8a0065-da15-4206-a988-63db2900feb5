# PowerShell script to downgrade Ollama to version 0.1.30

# Stop Ollama service
Write-Host "Stopping Ollama service..."
ollama stop

# Create a directory for the download if it doesn't exist
$downloadDir = "$env:TEMP\ollama_downgrade"
if (-not (Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir | Out-Null
}

# Download Ollama v0.1.30
Write-Host "Downloading Ollama v0.1.30..."
$downloadUrl = "https://github.com/ollama/ollama/releases/download/v0.1.30/ollama-windows-amd64.zip"
$zipPath = "$downloadDir\ollama-v0.1.30.zip"
Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath

# Extract the zip file
Write-Host "Extracting Ollama v0.1.30..."
Expand-Archive -Path $zipPath -DestinationPath $downloadDir -Force

# Determine Ollama installation directory
$ollamaPath = (Get-Command ollama -ErrorAction SilentlyContinue).Source
if (-not $ollamaPath) {
    Write-Host "Error: Could not find Ollama installation. Please ensure Ollama is installed and in your PATH."
    exit 1
}
$ollamaDir = Split-Path -Parent $ollamaPath

# Backup current Ollama executable
$backupPath = "$ollamaDir\ollama.backup"
Write-Host "Backing up current Ollama executable to $backupPath..."
Copy-Item -Path $ollamaPath -Destination $backupPath -Force

# Replace with v0.1.30
Write-Host "Installing Ollama v0.1.30..."
Copy-Item -Path "$downloadDir\ollama.exe" -Destination $ollamaDir -Force

# Verify installation
Write-Host "Verifying installation..."
$version = & ollama --version
Write-Host "Installed Ollama version: $version"

Write-Host "Downgrade complete. If you need to revert, you can restore from the backup at $backupPath"