# Netlify configuration for SDQ Application
# 
# COPYRIGHT NOTICE:
# The Strengths and Difficulties Questionnaire (SDQ) is copyrighted material 
# owned by Youthinmind Ltd. This electronic implementation is used with 
# permission and acknowledgement of the copyright holder.

[build]
  # No build process needed - static HTML file
  publish = "."
  
[build.environment]
  # Environment variables (if needed)
  NODE_VERSION = "18"

[[headers]]
  # Apply security headers to all pages
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self';"

[[redirects]]
  # Redirect root to the main app
  from = "/"
  to = "/sdq_app.html"
  status = 200

[[redirects]]
  # Handle 404s by serving the main app (SPA behavior)
  from = "/*"
  to = "/sdq_app.html"
  status = 404

# Custom headers for the main app
[[headers]]
  for = "/sdq_app.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"
    
# Copyright and licensing information
# SDQ © Youthinmind Ltd. All rights reserved.
# For licensing information: <EMAIL>
# Website: https://www.sdqinfo.org/
