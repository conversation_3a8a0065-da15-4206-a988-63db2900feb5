# SDQ Electronic Implementation

## Copyright Notice and Acknowledgement

**IMPORTANT:** The Strengths and Difficulties Questionnaire (SDQ) is copyrighted material owned by Youthinmind Ltd.

### Copyright Information
- **Copyright Holder:** Youthinmind Ltd
- **All Rights Reserved:** © Youthinmind Ltd
- **Website:** https://www.sdqinfo.org/
- **Contact:** <EMAIL>

### Acknowledgement
I acknowledge that the SDQ is copyrighted material owned by Youthinmind, and I am committed to including all required copyright and attribution notices in this electronic implementation.

This implementation is for:
- Non-profit use
- Internal healthcare settings
- Clinical assessment purposes
- Compliance-driven applications

### Licensing and Permissions
This electronic version is implemented with respect for copyright and willingness to comply with all licensing requirements. For any commercial use, distribution, or modification, please contact Youthinmind Ltd directly at:

- **Email:** <EMAIL>
- **Licensing inquiries:** <EMAIL>
- **SDQ Pro enquiries:** <EMAIL>
- **Scoring tools:** <EMAIL>

## About the SDQ

The Strengths and Difficulties Questionnaire (SDQ) is a brief behavioural screening questionnaire for children and young people aged 2-17 years. It exists in several versions to meet the needs of researchers, clinicians and educationalists.

### Key Features
- **Age Range:** 2-17 years
- **Versions:** Parent (4-17), Teacher (4-17), Youth (11-17), Nursery (2-4)
- **Modes:** Intake, Follow-up, Closure assessments
- **Domains:** Emotional symptoms, Conduct problems, Hyperactivity/inattention, Peer relationship problems, Prosocial behaviour
- **Administration Time:** 5-10 minutes
- **Scoring:** Automated with age-appropriate clinical interpretation bands
- **Privacy:** Uses initials only for participant identification

## Technical Implementation

### Deployment on Netlify
This is a single-file HTML application that can be deployed on Netlify:

1. **Simple Deployment:** Drag and drop the `sdq_app.html` file to Netlify
2. **Git Deployment:** Connect this repository to Netlify for automatic deployments
3. **Configuration:** Uses `netlify.toml` for proper headers and redirects

### Features
- ✅ Offline-capable single-file application
- ✅ No server or database required
- ✅ Responsive design for mobile and desktop
- ✅ Enhanced CSV export with clinical bands and interpretation
- ✅ Real-time scoring and interpretation
- ✅ Visual charts and scoring bands
- ✅ Four questionnaire versions (Parent 4-17, Teacher 4-17, Youth 11-17, Nursery 2-4)
- ✅ Three assessment modes (Intake, Follow-up, Closure)
- ✅ Age-appropriate scoring thresholds
- ✅ Privacy-focused (initials only)
- ✅ URL parameters for direct access (e.g., ?v=Teacher&mode=followup)
- ✅ Follow-up mode includes change tracking

### Security
- Content Security Policy headers
- XSS protection
- Frame options protection
- No external data transmission (privacy-focused)

## Usage Guidelines

### Direct Access URLs
The application supports URL parameters for direct access to specific versions and modes:

- **Parent Intake:** `sdq_app.html?v=Parent&mode=intake`
- **Teacher Follow-up:** `sdq_app.html?v=Teacher&mode=followup`
- **Youth Closure:** `sdq_app.html?v=Youth&mode=closure`
- **Nursery Intake:** `sdq_app.html?v=Nursery&mode=intake`

### Version-Specific Features

#### Parent (4-17 years)
- 25 items with standard scoring
- 4 impact questions
- Generic 4-17 thresholds

#### Teacher (4-17 years)
- 25 items with standard scoring
- 2 impact questions (school-focused)
- Generic 4-17 thresholds

#### Youth (11-17 years)
- 25 items with standard scoring
- 4 impact questions (self-report)
- Generic 4-17 thresholds

#### Nursery (2-4 years)
- 22 items (age-appropriate)
- 3 impact questions
- Nursery-specific thresholds

### Assessment Modes

#### Intake
- Initial assessment
- Full questionnaire completion
- Baseline scoring

#### Follow-up
- Progress monitoring
- Includes "Change since last assessment" question
- Comparison with previous scores

#### Closure
- Final assessment
- Treatment outcome evaluation
- Discharge planning support

### Clinical Use
This tool is intended for use by qualified healthcare professionals in clinical settings for:
- Screening and assessment
- Treatment planning
- Progress monitoring
- Research (with appropriate permissions)

### Data Privacy
- All data remains local to the user's device
- No information is transmitted to external servers
- Uses initials only for participant identification (enhanced privacy)
- CSV export for local record keeping and integration with clinical systems
- Compliant with healthcare privacy requirements (HIPAA, GDPR)
- No persistent storage - data cleared on page refresh

## Contact for Permissions

For any questions about licensing, permissions, or proper use of the SDQ:

**Youthinmind Ltd**
- Website: https://www.sdqinfo.org/
- Email: <EMAIL>
- Phone: +44 (0)20 7580 7145

### Professional Contact Template
When contacting Youthinmind for permissions, include:
- Your full name and professional title
- Hospital/organization name and location
- Intended use case (clinical assessment, research, etc.)
- Implementation details
- Commitment to copyright compliance

---

**Disclaimer:** This implementation respects the intellectual property rights of Youthinmind Ltd and is created for legitimate clinical and educational purposes within healthcare settings.
