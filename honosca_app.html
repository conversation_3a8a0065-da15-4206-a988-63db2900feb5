<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>HoNOS / HoNOS65+ / HoNOSCA — Outcome Scales (All-in-one)</title>
  <meta name="description" content="Single-file HoNOS suite (Adult, Older Adults, Children & Adolescents). Client-side only. Australian English.">
  
  <!-- 
  ///////////////////////////////////////////////////////////////////////////////
  // COPYRIGHT NOTICE AND ACKNOWLEDGEMENT
  ///////////////////////////////////////////////////////////////////////////////
  
  The Health of the Nation Outcome Scales for Children and Adolescents (HoNOSCA) 
  is copyrighted material owned by the Royal College of Psychiatrists.
  
  © Royal College of Psychiatrists. All rights reserved.
  
  This electronic implementation is used with permission and acknowledgement 
  of the copyright holder. The HoNOSCA assessment content, scoring algorithms, 
  and interpretation guidelines remain the intellectual property of the 
  Royal College of Psychiatrists.
  
  For more information about HoNOSCA, licensing, or permissions:
  - Website: https://www.rcpsych.ac.uk/
  - Contact: Royal College of Psychiatrists
  
  ACKNOWLEDGEMENT:
  I acknowledge that HoNOSCA is copyrighted material owned by the Royal College 
  of Psychiatrists, and I am committed to including all required copyright and 
  attribution notices in this electronic implementation.
  
  This implementation is for non-profit, internal, compliance-driven use 
  within healthcare settings for clinical assessment purposes.
  
  ///////////////////////////////////////////////////////////////////////////////
  -->
  
  <style>
    :root{--bg:#0b1020;--card:#121936;--ink:#e9eefc;--muted:#9fb2ff;--hot:#e85d5d;--warm:#f0c24b;--cool:#58c07b;}
    *{box-sizing:border-box}body{margin:0;background:var(--bg);color:var(--ink);font:14px/1.5 system-ui,Segoe UI,Roboto,Helvetica,Arial}
    header{padding:20px;display:flex;gap:12px;align-items:center;justify-content:space-between;background:#0f1730;border-bottom:1px solid #1c2550}
    h1{margin:0;font-size:18px}
    .container{max-width:1100px;margin:24px auto;padding:0 16px}
    .card{background:var(--card);border:1px solid #1d2658;border-radius:16px;box-shadow:0 12px 30px rgba(0,0,0,.25)}
    .row{display:grid;gap:16px}
    .row.cols-2{grid-template-columns:1fr 1fr}
    .row.cols-3{grid-template-columns:1fr 1fr 1fr}
    .pad{padding:16px}
    label{display:block;margin:0 0 6px 2px;color:var(--muted);font-size:12px}
    select,button,input[type="text"]{width:100%;padding:10px 12px;border-radius:10px;border:1px solid #2a3476;background:#0f1730;color:var(--ink);outline:none}
    button{cursor:pointer;border:1px solid #33409a;background:#1a2456}
    button:hover{filter:brightness(1.1)}
    table{width:100%;border-collapse:collapse;border-spacing:0}
    th,td{padding:10px;border-bottom:1px solid #1b2350;text-align:left}
    th{color:var(--muted);font-weight:600}
    .pill{display:inline-block;padding:2px 8px;border-radius:999px;font-size:12px}
    .ok{background:rgba(88,192,123,.2);color:#9be3b5;border:1px solid #2d8b57}
    .warn{background:rgba(240,194,75,.2);color:#ffe199;border:1px solid #aa7e21}
    .bad{background:rgba(232,93,93,.25);color:#ffc1c1;border:1px solid #a83c3c}
    .low{background:rgba(99,153,230,.18);color:#bcd3ff;border:1px solid #4c6cc9}
    .grid-scores{display:grid;grid-template-columns:repeat(3,minmax(0,1fr));gap:10px}
    .score{padding:10px;border:1px solid #273081;border-radius:12px;background:#0d1530}
    .score h4{margin:0 0 4px 0}
    footer{padding:16px;color:#9bb0ff;text-align:center;font-size:12px}
    canvas{width:100%!important;height:280px!important;background:#0d1530;border:1px solid #22306a;border-radius:12px}
    .hint{color:#a6b6ff;font-size:12px}
    .flex{display:flex;gap:12px;align-items:center;flex-wrap:wrap}
    .spacer{height:8px}
    .small{font-size:12px;color:#9fb2ff}
    
    /* Mobile optimizations */
    @media (max-width: 768px) {
      header{flex-direction:column;gap:8px;padding:16px}
      h1{font-size:16px;text-align:center}
      .flex{justify-content:center;gap:8px}
      .container{margin:16px auto;padding:0 12px}
      .row.cols-2{grid-template-columns:1fr}
      .row.cols-3{grid-template-columns:1fr}
      table{font-size:12px}
      th,td{padding:8px 4px}
      select,button,input[type="text"]{padding:12px 8px;font-size:16px}
      .score{padding:8px}
      canvas{height:200px!important}
      .grid-scores{grid-template-columns:1fr 1fr;gap:8px}
    }
    
    @media (max-width: 480px) {
      header{padding:12px}
      .container{padding:0 8px}
      table{font-size:11px}
      th,td{padding:6px 2px}
      .flex{flex-direction:column;width:100%}
      .flex select, .flex button{margin-bottom:8px}
      .grid-scores{grid-template-columns:1fr}
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
</head>
<body>
<header class="card pad">
  <h1>Health of the Nation Outcome Scales for Children and Adolescents (HoNOSCA)</h1>
  <div class="flex">
    <label for="version">Version</label>
    <select id="version"></select>
    <label for="mode">Mode</label>
    <select id="mode">
      <option value="intake">Intake</option>
      <option value="followup">Follow-up</option>
      <option value="closure">Closure</option>
    </select>
    <button id="reset">Reset form</button>
    <button id="exportCsv">Export CSV</button>
    <button id="exportText">Export Text</button>
    <button id="printPdf">Print/PDF</button>
  </div>
</header>

<div class="container">
  <div class="card pad">
    <div class="row cols-3">
      <div>
        <label>Participant Initials</label>
        <input id="participant" type="text" placeholder="e.g. J.D." maxlength="10">
      </div>
      <div>
        <label>Date</label>
        <input id="when" type="text" placeholder="YYYY-MM-DD">
      </div>
      <div>
        <label>Assessor</label>
        <input id="assessor" type="text" placeholder="Clinician name">
      </div>
    </div>

    <div class="spacer"></div>
    <div class="hint">
      <strong>Current Version:</strong> <span id="versionDisplay"></span><br>
      Rate each scale from 0-4 based on the most severe problem in the past two weeks.
    </div>
    <div class="spacer"></div>

    <table id="table">
      <thead>
        <tr>
          <th style="width:60px">Scale</th>
          <th>Description</th>
          <th style="width:200px">Rating</th>
          <th style="width:90px">Score</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>

    <div class="spacer"></div>
    <div class="row cols-2">
      <div>
        <h3 class="small">Additional Information</h3>
        <div id="additionalContainer" class="row"></div>
      </div>
      <div>
        <h3 class="small">Scores & Interpretation</h3>
        <div class="grid-scores" id="scoreGrid"></div>
      </div>
    </div>

    <div class="spacer"></div>
    <h3 class="small">Score Profile Chart</h3>
    <canvas id="chart"></canvas>
  </div>
</div>

<footer>
  <div style="margin-bottom: 12px;">
    <strong>Copyright Notice:</strong> The Health of the Nation Outcome Scales for Children and Adolescents (HoNOSCA) 
    is copyrighted material owned by the Royal College of Psychiatrists. 
    This electronic implementation is used with permission and acknowledgement of the copyright holder.
  </div>
  <div style="margin-bottom: 8px;">
    © Royal College of Psychiatrists. All rights reserved. | For more information: <a href="https://www.rcpsych.ac.uk/" target="_blank" style="color: #9fb2ff;">www.rcpsych.ac.uk</a>
  </div>
  <div style="font-size: 11px; color: #7a8ccc;">
    Offline single-file app. Host on Netlify by dropping this HTML. No server required.
  </div>
</footer>

<script>
// ----- HoNOSCA Scales -----
const HONOSCA_SCALES = [
  {
    id: 1,
    name: "Disruptive, antisocial or aggressive behaviour",
    description: "Include such behaviour due to any cause, e.g. drugs, alcohol, dementia, psychosis, depression, etc.",
    examples: "Aggression, vandalism, petty crime, lying, sexual disinhibition, etc."
  },
  {
    id: 2,
    name: "Overactivity, attention and concentration problems",
    description: "Include overactivity and restlessness due to any cause.",
    examples: "Hyperactivity, distractibility, concentration problems, restlessness."
  },
  {
    id: 3,
    name: "Non-accidental self-injury",
    description: "Include self-harm due to any cause.",
    examples: "Suicide attempts, self-cutting, self-hitting, eating disorders with self-harm."
  },
  {
    id: 4,
    name: "Alcohol, substance/solvent misuse",
    description: "Include problems with alcohol or other substances.",
    examples: "Alcohol abuse, drug misuse, solvent abuse, prescription drug misuse."
  },
  {
    id: 5,
    name: "Scholastic or language skills",
    description: "Include problems with reading, spelling, arithmetic, speech or language.",
    examples: "Learning difficulties, dyslexia, speech delays, language disorders."
  },
  {
    id: 6,
    name: "Physical illness or disability problems",
    description: "Include illness or disability that limits or prevents movement, or impairs sight or hearing.",
    examples: "Chronic illness, physical disabilities, sensory impairments."
  },
  {
    id: 7,
    name: "Hallucinations and delusions",
    description: "Include hallucinations and delusions irrespective of diagnosis.",
    examples: "Auditory/visual hallucinations, paranoid delusions, thought disorders."
  },
  {
    id: 8,
    name: "Non-organic somatic symptoms",
    description: "Include physical symptoms not due to identified physical illness.",
    examples: "Headaches, stomach aches, fatigue without medical cause."
  },
  {
    id: 9,
    name: "Emotional and related symptoms",
    description: "Include depression, anxiety, worry, fears, phobias, obsessions or compulsions.",
    examples: "Depression, anxiety disorders, phobias, OCD, emotional distress."
  },
  {
    id: 10,
    name: "Peer relationships",
    description: "Include problems with making and sustaining peer friendships and relationships.",
    examples: "Social isolation, bullying, difficulty making friends, peer rejection."
  },
  {
    id: 11,
    name: "Self-care and independence",
    description: "Include problems with basic activities of daily living appropriate to age.",
    examples: "Personal hygiene, dressing, eating, age-appropriate independence."
  },
  {
    id: 12,
    name: "Family life and relationships",
    description: "Include problems with relationships with parents, siblings or other family members.",
    examples: "Family conflict, attachment problems, family breakdown."
  },
  {
    id: 13,
    name: "Poor school attendance",
    description: "Include problems with school attendance for any reason.",
    examples: "Truancy, school refusal, frequent absences, exclusions."
  }
];

// ----- Rating Scale -----
const RATING_OPTIONS = [
  { value: 0, label: "0 - No problem", description: "No problem of this type" },
  { value: 1, label: "1 - Minor problem", description: "Minor problem requiring no action" },
  { value: 2, label: "2 - Mild problem", description: "Mild problem but definitely present" },
  { value: 3, label: "3 - Moderately severe", description: "Moderately severe problem" },
  { value: 4, label: "4 - Severe problem", description: "Severe to very severe problem" }
];

// ----- Version Profiles -----
const VERSION_PROFILES = {
  Standard: {
    label: "HoNOSCA Standard",
    scales: HONOSCA_SCALES,
    additionalItems: ["Global Assessment", "Known to Services"]
  }
};

// Read query params
const qp = new URLSearchParams(location.search);
const state = {
  version: qp.get("v") && VERSION_PROFILES[qp.get("v")] ? qp.get("v") : "Standard",
  mode: qp.get("mode") || "intake",
  ratings: Array(13).fill(0),
  additional: {}
};

// ----- Utils -----
function currentProfile(){ return VERSION_PROFILES[state.version] || VERSION_PROFILES.Standard; }
function byId(id){ return document.getElementById(id); }

function updateVersionDisplay(){
  const profile = currentProfile();
  byId("versionDisplay").textContent = `${profile.label} - Clinical outcome measurement`;
}

function calcScores(){
  const total = state.ratings.reduce((sum, rating) => sum + rating, 0);
  const behavioural = state.ratings.slice(0, 4).reduce((sum, rating) => sum + rating, 0); // Scales 1-4
  const impairment = state.ratings.slice(4, 8).reduce((sum, rating) => sum + rating, 0);   // Scales 5-8
  const symptoms = state.ratings.slice(8, 12).reduce((sum, rating) => sum + rating, 0);    // Scales 9-12
  const social = state.ratings[12] || 0; // Scale 13

  return { total, behavioural, impairment, symptoms, social };
}

function getInterpretation(score, maxScore) {
  const percentage = (score / maxScore) * 100;
  if (percentage <= 25) return ["Low", "ok"];
  if (percentage <= 50) return ["Moderate", "warn"];
  if (percentage <= 75) return ["High", "bad"];
  return ["Very High", "bad"];
}

function renderTable(){
  const tb = byId("table").querySelector("tbody");
  tb.innerHTML = "";
  const scales = currentProfile().scales;

  scales.forEach((scale, index) => {
    const tr = document.createElement("tr");

    // Scale number
    const tdNo = document.createElement("td");
    tdNo.textContent = scale.id;

    // Description
    const tdDesc = document.createElement("td");
    tdDesc.innerHTML = `<strong>${scale.name}</strong><br><small style="color: var(--muted);">${scale.description}</small>`;

    // Rating dropdown
    const tdRating = document.createElement("td");
    const sel = document.createElement("select");
    RATING_OPTIONS.forEach(option => {
      const opt = document.createElement("option");
      opt.value = option.value;
      opt.textContent = option.label;
      opt.title = option.description;
      sel.appendChild(opt);
    });
    sel.value = state.ratings[index] || 0;
    sel.onchange = (e) => {
      state.ratings[index] = parseInt(e.target.value);
      update();
    };
    tdRating.appendChild(sel);

    // Score display
    const tdScore = document.createElement("td");
    tdScore.textContent = state.ratings[index] || 0;
    tdScore.style.textAlign = "center";
    tdScore.style.fontWeight = "bold";

    tr.appendChild(tdNo);
    tr.appendChild(tdDesc);
    tr.appendChild(tdRating);
    tr.appendChild(tdScore);
    tb.appendChild(tr);
  });
}

function renderAdditional(){
  const div = byId("additionalContainer");
  div.innerHTML = "";

  if (state.mode === "followup") {
    const wrap = document.createElement("div");
    wrap.className = "row cols-2";
    wrap.innerHTML = `
      <div>
        <label>Overall change since last assessment</label>
        <select id="overallChange">
          <option value="">—</option>
          <option value="much_better">Much better</option>
          <option value="better">Better</option>
          <option value="same">About the same</option>
          <option value="worse">Worse</option>
          <option value="much_worse">Much worse</option>
        </select>
      </div>
      <div>
        <label>Treatment response</label>
        <select id="treatmentResponse">
          <option value="">—</option>
          <option value="excellent">Excellent</option>
          <option value="good">Good</option>
          <option value="fair">Fair</option>
          <option value="poor">Poor</option>
        </select>
      </div>
    `;
    div.appendChild(wrap);
  }
}

function renderScores(){
  const scores = calcScores();

  // Score grid
  const g = byId("scoreGrid");
  g.innerHTML = `
    <div class="score">
      <h4>Total Score</h4>
      <div><strong>${scores.total}</strong> <span class="pill ${getInterpretation(scores.total, 52)[1]}">${getInterpretation(scores.total, 52)[0]}</span></div>
    </div>
    <div class="score">
      <h4>Behavioural (1-4)</h4>
      <div><strong>${scores.behavioural}</strong> <span class="pill ${getInterpretation(scores.behavioural, 16)[1]}">${getInterpretation(scores.behavioural, 16)[0]}</span></div>
    </div>
    <div class="score">
      <h4>Impairment (5-8)</h4>
      <div><strong>${scores.impairment}</strong> <span class="pill ${getInterpretation(scores.impairment, 16)[1]}">${getInterpretation(scores.impairment, 16)[0]}</span></div>
    </div>
    <div class="score">
      <h4>Symptoms (9-12)</h4>
      <div><strong>${scores.symptoms}</strong> <span class="pill ${getInterpretation(scores.symptoms, 16)[1]}">${getInterpretation(scores.symptoms, 16)[0]}</span></div>
    </div>
    <div class="score">
      <h4>Social (13)</h4>
      <div><strong>${scores.social}</strong> <span class="pill ${getInterpretation(scores.social, 4)[1]}">${getInterpretation(scores.social, 4)[0]}</span></div>
    </div>
  `;

  // Chart
  const chartData = {
    labels: HONOSCA_SCALES.map(scale => `Scale ${scale.id}`),
    values: state.ratings
  };

  if(!window.honosca_chart){
    window.honosca_chart = new Chart(byId("chart").getContext("2d"), {
      type: "bar",
      data: {
        labels: chartData.labels,
        datasets: [{
          label: "HoNOSCA Ratings",
          data: chartData.values,
          backgroundColor: 'rgba(159, 178, 255, 0.6)',
          borderColor: 'rgba(159, 178, 255, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: { legend: { display: false } },
        scales: {
          y: { beginAtZero: true, max: 4, ticks: { stepSize: 1 } },
          x: { grid: { display: false } }
        }
      }
    });
  } else {
    window.honosca_chart.data.datasets[0].data = chartData.values;
    window.honosca_chart.update();
  }
}

function update(){
  updateVersionDisplay();
  renderScores();
}

// Reset/export
byId("reset").onclick = () => {
  state.ratings = Array(13).fill(0);
  renderTable();
  renderAdditional();
  update();
};

byId("exportCsv").onclick = () => {
  const scores = calcScores();
  const fileSafe = s => (s||"").trim().replace(/\s+/g,"_");
  const overallChange = byId("overallChange") ? byId("overallChange").value : "";
  const treatmentResponse = byId("treatmentResponse") ? byId("treatmentResponse").value : "";

  const csv = [
    ["Participant Initials", byId("participant").value],
    ["Date", byId("when").value],
    ["Assessor", byId("assessor").value],
    ["Version", currentProfile().label],
    ["Mode", state.mode],
    ...(overallChange ? [["Overall Change", overallChange]] : []),
    ...(treatmentResponse ? [["Treatment Response", treatmentResponse]] : []),
    [],
    ["Scale", "Rating", "Description"],
    ...HONOSCA_SCALES.map((scale, i) => [
      `${scale.id}. ${scale.name}`,
      state.ratings[i],
      getInterpretation(state.ratings[i], 4)[0]
    ]),
    [],
    ["Summary Scores", "Score", "Interpretation"],
    ["Total Score", scores.total, getInterpretation(scores.total, 52)[0]],
    ["Behavioural (1-4)", scores.behavioural, getInterpretation(scores.behavioural, 16)[0]],
    ["Impairment (5-8)", scores.impairment, getInterpretation(scores.impairment, 16)[0]],
    ["Symptoms (9-12)", scores.symptoms, getInterpretation(scores.symptoms, 16)[0]],
    ["Social (13)", scores.social, getInterpretation(scores.social, 4)[0]]
  ].map(r => r.join(",")).join("\n");

  const blob = new Blob([csv], {type: "text/csv"});
  const a = document.createElement("a");
  a.href = URL.createObjectURL(blob);
  a.download = `HoNOSCA_${state.mode}_${fileSafe(byId("participant").value)}.csv`;
  a.click();
};

// Text Export
byId("exportText").onclick = () => {
  const scores = calcScores();
  const fileSafe = s => (s||"").trim().replace(/\s+/g,"_");
  const overallChange = byId("overallChange") ? byId("overallChange").value : "";
  const treatmentResponse = byId("treatmentResponse") ? byId("treatmentResponse").value : "";
  const currentDate = new Date().toLocaleDateString();

  const textReport = `
HEALTH OF THE NATION OUTCOME SCALES FOR CHILDREN AND ADOLESCENTS (HoNOSCA)
================================================================================

ASSESSMENT INFORMATION
----------------------
Participant Initials: ${byId("participant").value || "Not specified"}
Assessment Date: ${byId("when").value || currentDate}
Assessor: ${byId("assessor").value || "Not specified"}
Version: ${currentProfile().label}
Assessment Mode: ${state.mode.charAt(0).toUpperCase() + state.mode.slice(1)}
${overallChange ? `Overall Change: ${overallChange.replace(/_/g, ' ')}` : ''}
${treatmentResponse ? `Treatment Response: ${treatmentResponse}` : ''}

SCALE RATINGS
-------------
${HONOSCA_SCALES.map((scale, i) =>
  `${scale.id.toString().padStart(2)}. ${scale.name.padEnd(50)} Rating: ${state.ratings[i]} (${getInterpretation(state.ratings[i], 4)[0]})`
).join('\n')}

SUMMARY SCORES
--------------
Total Score (0-52):           ${scores.total.toString().padStart(2)} (${getInterpretation(scores.total, 52)[0]})
Behavioural Problems (1-4):   ${scores.behavioural.toString().padStart(2)} (${getInterpretation(scores.behavioural, 16)[0]})
Impairment (5-8):             ${scores.impairment.toString().padStart(2)} (${getInterpretation(scores.impairment, 16)[0]})
Symptoms (9-12):              ${scores.symptoms.toString().padStart(2)} (${getInterpretation(scores.symptoms, 16)[0]})
Social Functioning (13):      ${scores.social.toString().padStart(2)} (${getInterpretation(scores.social, 4)[0]})

INTERPRETATION GUIDE
--------------------
Rating Scale:
0 = No problem
1 = Minor problem requiring no action
2 = Mild problem but definitely present
3 = Moderately severe problem
4 = Severe to very severe problem

Interpretation Bands:
Low = 0-25% of maximum score
Moderate = 26-50% of maximum score
High = 51-75% of maximum score
Very High = 76-100% of maximum score

================================================================================
© Royal College of Psychiatrists. HoNOSCA is copyrighted material.
Generated: ${new Date().toLocaleString()}
================================================================================
`.trim();

  const blob = new Blob([textReport], {type: "text/plain"});
  const a = document.createElement("a");
  a.href = URL.createObjectURL(blob);
  a.download = `HoNOSCA_Report_${state.mode}_${fileSafe(byId("participant").value)}.txt`;
  a.click();
};

// Print/PDF Export
byId("printPdf").onclick = () => {
  const scores = calcScores();
  const overallChange = byId("overallChange") ? byId("overallChange").value : "";
  const treatmentResponse = byId("treatmentResponse") ? byId("treatmentResponse").value : "";
  const currentDate = new Date().toLocaleDateString();

  // Create a new window for printing
  const printWindow = window.open('', '_blank');
  printWindow.document.write(`
<!DOCTYPE html>
<html>
<head>
  <title>HoNOSCA Assessment Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.4; }
    .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
    .section { margin-bottom: 20px; }
    .section h3 { background: #f0f0f0; padding: 8px; margin: 0 0 10px 0; border-left: 4px solid #333; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f5f5f5; font-weight: bold; }
    .score-high { background-color: #ffebee; }
    .score-moderate { background-color: #fff3e0; }
    .score-low { background-color: #e8f5e8; }
    .footer { margin-top: 30px; padding-top: 10px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
    @media print {
      body { margin: 0; }
      .no-print { display: none; }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Health of the Nation Outcome Scales for Children and Adolescents</h1>
    <h2>(HoNOSCA)</h2>
  </div>

  <div class="section">
    <h3>Assessment Information</h3>
    <table>
      <tr><td><strong>Participant Initials:</strong></td><td>${byId("participant").value || "Not specified"}</td></tr>
      <tr><td><strong>Assessment Date:</strong></td><td>${byId("when").value || currentDate}</td></tr>
      <tr><td><strong>Assessor:</strong></td><td>${byId("assessor").value || "Not specified"}</td></tr>
      <tr><td><strong>Version:</strong></td><td>${currentProfile().label}</td></tr>
      <tr><td><strong>Assessment Mode:</strong></td><td>${state.mode.charAt(0).toUpperCase() + state.mode.slice(1)}</td></tr>
      ${overallChange ? `<tr><td><strong>Overall Change:</strong></td><td>${overallChange.replace(/_/g, ' ')}</td></tr>` : ''}
      ${treatmentResponse ? `<tr><td><strong>Treatment Response:</strong></td><td>${treatmentResponse}</td></tr>` : ''}
    </table>
  </div>

  <div class="section">
    <h3>Scale Ratings</h3>
    <table>
      <thead>
        <tr><th>Scale</th><th>Description</th><th>Rating</th><th>Interpretation</th></tr>
      </thead>
      <tbody>
        ${HONOSCA_SCALES.map((scale, i) => {
          const rating = state.ratings[i];
          const interpretation = getInterpretation(rating, 4)[0];
          const cssClass = interpretation === 'Low' ? 'score-low' :
                          interpretation === 'Moderate' ? 'score-moderate' : 'score-high';
          return `<tr class="${cssClass}">
            <td>${scale.id}</td>
            <td>${scale.name}</td>
            <td style="text-align: center; font-weight: bold;">${rating}</td>
            <td>${interpretation}</td>
          </tr>`;
        }).join('')}
      </tbody>
    </table>
  </div>

  <div class="section">
    <h3>Summary Scores</h3>
    <table>
      <thead>
        <tr><th>Domain</th><th>Score</th><th>Maximum</th><th>Interpretation</th></tr>
      </thead>
      <tbody>
        <tr><td>Total Score</td><td style="text-align: center; font-weight: bold;">${scores.total}</td><td>52</td><td>${getInterpretation(scores.total, 52)[0]}</td></tr>
        <tr><td>Behavioural Problems (1-4)</td><td style="text-align: center; font-weight: bold;">${scores.behavioural}</td><td>16</td><td>${getInterpretation(scores.behavioural, 16)[0]}</td></tr>
        <tr><td>Impairment (5-8)</td><td style="text-align: center; font-weight: bold;">${scores.impairment}</td><td>16</td><td>${getInterpretation(scores.impairment, 16)[0]}</td></tr>
        <tr><td>Symptoms (9-12)</td><td style="text-align: center; font-weight: bold;">${scores.symptoms}</td><td>16</td><td>${getInterpretation(scores.symptoms, 16)[0]}</td></tr>
        <tr><td>Social Functioning (13)</td><td style="text-align: center; font-weight: bold;">${scores.social}</td><td>4</td><td>${getInterpretation(scores.social, 4)[0]}</td></tr>
      </tbody>
    </table>
  </div>

  <div class="section">
    <h3>Rating Scale Guide</h3>
    <table>
      <tr><td><strong>0</strong></td><td>No problem</td></tr>
      <tr><td><strong>1</strong></td><td>Minor problem requiring no action</td></tr>
      <tr><td><strong>2</strong></td><td>Mild problem but definitely present</td></tr>
      <tr><td><strong>3</strong></td><td>Moderately severe problem</td></tr>
      <tr><td><strong>4</strong></td><td>Severe to very severe problem</td></tr>
    </table>
  </div>

  <div class="footer">
    <p><strong>Copyright Notice:</strong> © Royal College of Psychiatrists. HoNOSCA is copyrighted material.</p>
    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
  </div>
</body>
</html>
  `);

  printWindow.document.close();
  printWindow.focus();

  // Wait for content to load then print
  setTimeout(() => {
    printWindow.print();
  }, 500);
};

// Populate version select
const vSel = byId("version");
vSel.innerHTML = Object.entries(VERSION_PROFILES)
  .map(([k,p]) => `<option value="${k}">${p.label}</option>`).join("");
vSel.value = state.version;

// Mode select initialization
byId("mode").value = state.mode;

// Version switch
byId("version").onchange = (e) => {
  state.version = e.target.value;
  renderTable();
  renderAdditional();
  update();
};

// Mode switch
byId("mode").onchange = (e) => {
  state.mode = e.target.value;
  renderAdditional();
  update();
};

// Init
renderTable();
renderAdditional();
update();
</script>
</body>
</html>
