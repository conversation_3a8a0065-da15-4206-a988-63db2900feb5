# Ollama Memory Optimization Solutions

This repository contains solutions to address memory issues with Ollama, particularly when used with Trinity. These solutions are designed to help mitigate memory leaks and improve stability when running Ollama for extended periods or with multiple requests.

## Problem

Ollama may experience memory leaks or excessive memory usage, especially when:
- Running for extended periods
- Processing multiple requests in sequence
- Being used with frameworks like Trinity

These issues can lead to degraded performance, out-of-memory errors, or system instability.

## Solutions

This repository provides four different approaches to address Ollama memory issues:

1. **Downgrade to a stable version** - Revert to Ollama v0.1.30 which may have fewer memory issues
2. **Force memory cleanup between requests** - Use Python's garbage collection to free memory after each request
3. **Modify API connection handling** - Explicitly close connections after each API call
4. **Run with memory limits** - Start Ollama with specific memory constraints

You can use these solutions individually or combine them for better results.

## 1. Downgrading Ollama

The `downgrade_ollama.ps1` script helps you downgrade to Ollama v0.1.30, which may have fewer memory issues.

### Usage

```powershell
.\downgrade_ollama.ps1
```

This script will:
1. Stop the running Ollama service
2. Download Ollama v0.1.30
3. Back up your current Ollama executable
4. Replace it with v0.1.30
5. Verify the installation

If you need to revert to your original version, the script creates a backup at the same location with a `.backup` extension.

## 2. Memory Cleanup Between Requests

The `trinity_memory_cleanup.py` module provides functions to force memory cleanup after each agent response.

### Usage

```python
# Import the module
from trinity_memory_cleanup import cleanup_after_agent, wrap_agent_call

# Option 1: Call cleanup explicitly after each agent
def run_trinity_agent(prompt):
    response = agent.get_response(prompt)
    cleanup_after_agent()  # Explicit cleanup
    return response

# Option 2: Use the wrapper function
def run_trinity_agent_wrapped(prompt):
    return wrap_agent_call(agent.get_response, prompt)
```

This approach forces Python's garbage collection and adds a small delay to give Ollama time to release memory.

## 3. API Connection Management

The `ollama_api_connection_management.py` module provides a class to manage API connections to Ollama, ensuring they are properly closed after each request.

### Usage

```python
# Import the module
from ollama_api_connection_management import OllamaAPIManager

# Create an instance of the Ollama API Manager
ollama = OllamaAPIManager()

# Generate a response
response = ollama.generate(
    model="llama2",
    prompt="What is the capital of France?",
    system="You are a helpful assistant."
)

# Chat with a model
messages = [
    {"role": "user", "content": "What is the capital of France?"}
]
response = ollama.chat(model="llama2", messages=messages)
```

This approach ensures that connections are explicitly closed after each API call, which can help prevent memory leaks.

## 4. Running with Memory Limits

The `run_ollama_with_limits.ps1` script helps you run Ollama with specific memory constraints.

### Usage

```powershell
# Run with default limits (1 model, 1 parallel request, 4GB memory)
.\run_ollama_with_limits.ps1

# Run with custom limits
.\run_ollama_with_limits.ps1 -MaxLoadedModels 2 -NumParallel 2 -MaxMemory "8gb"
```

This approach limits the resources Ollama can use, which can help prevent it from consuming too much memory.

## Combining Solutions

For best results, you may want to combine multiple solutions:

1. Downgrade to a stable version using `downgrade_ollama.ps1`
2. Run with memory limits using `run_ollama_with_limits.ps1`
3. In your Trinity code, use both the connection management and memory cleanup approaches

## Troubleshooting

If you continue to experience memory issues:

- Try reducing the number of models loaded at once
- Increase the delay in `cleanup_after_agent()` (e.g., from 3 to 5 seconds)
- Consider using smaller models if available
- Monitor memory usage to identify patterns or specific triggers for leaks

## Contributing

Feel free to contribute additional solutions or improvements to the existing ones by submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.