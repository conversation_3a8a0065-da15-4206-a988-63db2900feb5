"""
Trinity Memory Cleanup Module

This module provides functions to help manage memory when using Ollama with Trinity.
It implements garbage collection and sleep between agent calls to help mitigate
Ollama memory leaks.
"""

import gc
import time

def cleanup_after_agent():
    """
    Force memory cleanup after an agent response.
    
    This function should be called after each agent response to help
    mitigate Ollama memory leaks. It forces Python garbage collection
    and gives Ollama time to release memory.
    
    Example usage:
        # After getting a response from an agent
        response = agent.get_response(prompt)
        cleanup_after_agent()  # Call this after each agent response
    """
    # Force Python garbage collection
    gc.collect()
    
    # Give Ollama time to release memory
    time.sleep(3)

def wrap_agent_call(agent_func, *args, **kwargs):
    """
    Wrapper function to automatically clean up after agent calls.
    
    This function wraps an agent call function and automatically
    performs memory cleanup after the call.
    
    Args:
        agent_func: The agent function to call
        *args, **kwargs: Arguments to pass to the agent function
        
    Returns:
        The result of the agent function call
        
    Example usage:
        # Instead of directly calling agent.get_response
        # response = agent.get_response(prompt)
        
        # Use the wrapper
        response = wrap_agent_call(agent.get_response, prompt)
    """
    try:
        # Call the agent function
        result = agent_func(*args, **kwargs)
        return result
    finally:
        # Always clean up, even if an exception occurs
        cleanup_after_agent()

# Example integration with <PERSON>
"""
# Import this module in your Trinity code
from trinity_memory_cleanup import cleanup_after_agent, wrap_agent_call

# Option 1: Call cleanup explicitly after each agent
def run_trinity_agent(prompt):
    response = agent.get_response(prompt)
    cleanup_after_agent()  # Explicit cleanup
    return response

# Option 2: Use the wrapper function
def run_trinity_agent_wrapped(prompt):
    return wrap_agent_call(agent.get_response, prompt)
"""