# Netlify configuration for HoNOSCA Application
# 
# COPYRIGHT NOTICE:
# The Health of the Nation Outcome Scales for Children and Adolescents (HoNOSCA) 
# is copyrighted material owned by the Royal College of Psychiatrists. 
# This electronic implementation is used with permission and acknowledgement 
# of the copyright holder.

[build]
  # No build process needed - static HTML file
  publish = "."
  
[build.environment]
  # Environment variables (if needed)
  NODE_VERSION = "18"

[[headers]]
  # Apply security headers to all pages
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self';"

[[redirects]]
  # Redirect root to the main app
  from = "/"
  to = "/honosca_app.html"
  status = 200

[[redirects]]
  # Handle 404s by serving the main app (SPA behavior)
  from = "/*"
  to = "/honosca_app.html"
  status = 404

# Custom headers for the main app
[[headers]]
  for = "/honosca_app.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"
    
# Copyright and licensing information
# HoNOSCA © Royal College of Psychiatrists. All rights reserved.
# For licensing information: Contact Royal College of Psychiatrists
# Website: https://www.rcpsych.ac.uk/
